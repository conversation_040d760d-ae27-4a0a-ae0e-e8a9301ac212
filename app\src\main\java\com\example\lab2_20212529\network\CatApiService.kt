package com.example.lab2_20212529.network

import com.example.lab2_20212529.data.CatImage
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

interface CatApiService {
    @GET("api/cats")
    suspend fun getCats(
        @Query("limit") limit: Int = 10,
        @Query("tags") tags: String? = null
    ): Response<List<CatImage>>
    
    companion object {
        const val BASE_URL = "https://cataas.com/"
        
        fun getImageUrl(catId: String, text: String? = null): String {
            return if (text.isNullOrEmpty()) {
                "${BASE_URL}cat/$catId"
            } else {
                "${BASE_URL}cat/$catId/says/$text"
            }
        }
    }
}
