package com.example.lab2_20212529;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.button.MaterialButton;

import java.util.ArrayList;

/**
 * HistorialActivity - Pantalla que muestra el historial de todas las interacciones
 * Muestra cada sesión con el número de imágenes visualizadas
 */
public class HistorialActivity extends AppCompatActivity {

    private LinearLayout llHistorialContainer;
    private MaterialButton btnVolverAJugar;
    
    private SharedPreferences sharedPreferences;
    private static final String PREF_NAME = "TeleCatHistorial";
    private static final String KEY_INTERACCIONES = "interacciones_count";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_historial);

        // Inicializar vistas
        initViews();

        // Configurar SharedPreferences
        sharedPreferences = getSharedPreferences(PREF_NAME, MODE_PRIVATE);

        // Obtener datos del Intent
        ArrayList<String> historialImagenes = getIntent().getStringArrayListExtra("historial");
        int cantidadImagenes = getIntent().getIntExtra("cantidadImagenes", 0);

        // Guardar nueva interacción
        saveNewInteraction(cantidadImagenes);

        // Mostrar historial
        displayHistorial();

        // Configurar botón
        setupButton();
    }

    private void initViews() {
        llHistorialContainer = findViewById(R.id.llHistorialContainer);
        btnVolverAJugar = findViewById(R.id.btnVolverAJugar);
    }

    /**
     * Guarda una nueva interacción en el historial
     */
    private void saveNewInteraction(int cantidadImagenes) {
        // Obtener número actual de interacciones
        int interaccionesCount = sharedPreferences.getInt(KEY_INTERACCIONES, 0);
        
        // Incrementar contador
        interaccionesCount++;
        
        // Guardar nueva interacción
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(KEY_INTERACCIONES, interaccionesCount);
        editor.putInt("interaccion_" + interaccionesCount + "_imagenes", cantidadImagenes);
        editor.apply();
    }

    /**
     * Muestra todo el historial de interacciones
     */
    private void displayHistorial() {
        // Limpiar container
        llHistorialContainer.removeAllViews();

        // Obtener número total de interacciones
        int totalInteracciones = sharedPreferences.getInt(KEY_INTERACCIONES, 0);

        if (totalInteracciones == 0) {
            // Mostrar mensaje si no hay historial
            TextView tvNoHistorial = new TextView(this);
            tvNoHistorial.setText("No hay interacciones en el historial");
            tvNoHistorial.setTextSize(16);
            tvNoHistorial.setTextColor(getResources().getColor(android.R.color.darker_gray));
            tvNoHistorial.setPadding(16, 32, 16, 32);
            llHistorialContainer.addView(tvNoHistorial);
            return;
        }

        // Mostrar cada interacción
        for (int i = 1; i <= totalInteracciones; i++) {
            int imagenesEnInteraccion = sharedPreferences.getInt("interaccion_" + i + "_imagenes", 0);
            addHistorialItem(i, imagenesEnInteraccion);
        }
    }

    /**
     * Agrega un item al historial
     */
    private void addHistorialItem(int numeroInteraccion, int cantidadImagenes) {
        // Inflar layout del item
        LayoutInflater inflater = LayoutInflater.from(this);
        View itemView = inflater.inflate(R.layout.item_historial, llHistorialContainer, false);

        // Configurar texto
        TextView tvTitulo = itemView.findViewById(R.id.tvInteraccionTitulo);
        String texto = "Interacción " + numeroInteraccion + ": " + cantidadImagenes + " imágenes";
        tvTitulo.setText(texto);

        // Agregar al container
        llHistorialContainer.addView(itemView);
    }

    /**
     * Configura el botón para volver a jugar
     */
    private void setupButton() {
        btnVolverAJugar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Volver al menú principal
                Intent intent = new Intent(HistorialActivity.this, MainActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
                finish();
            }
        });
    }

    @Override
    public void onBackPressed() {
        // Interceptar botón atrás para ir al menú principal
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        finish();
        super.onBackPressed();
    }
}
