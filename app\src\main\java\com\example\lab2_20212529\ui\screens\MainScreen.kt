package com.example.lab2_20212529.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.lab2_20212529.data.AppState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    uiState: AppState,
    onQuantityChange: (String) -> Unit,
    onTextSelectionChange: (String) -> Unit,
    onCustomTextChange: (String) -> Unit,
    onCheckConnection: () -> Unit,
    onStartGame: () -> Unit,
    onClearError: () -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val textOptions = listOf("Elegir", "Sí", "No")
    
    // Mostrar error si existe
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // El error se mostrará en el UI
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFE57373))
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Título
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 32.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFF3E0))
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "¡Bienvenido a",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF5D4037)
                )
                Text(
                    text = "TeleCat 🐱!",
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF5D4037)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "La app diseñada para generar memes de gatos y divertirte :)",
                    fontSize = 14.sp,
                    color = Color(0xFF8D6E63),
                    textAlign = TextAlign.Center
                )
            }
        }
        
        // Formulario
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Campo Cantidad
                OutlinedTextField(
                    value = uiState.quantity.toString(),
                    onValueChange = onQuantityChange,
                    label = { Text("Cantidad:") },
                    placeholder = { Text("Escribe aquí") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                // Dropdown Texto
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = uiState.selectedText,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Texto:") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        textOptions.forEach { option ->
                            DropdownMenuItem(
                                text = { Text(option) },
                                onClick = {
                                    onTextSelectionChange(option)
                                    expanded = false
                                }
                            )
                        }
                    }
                }
                
                // Campo Escribir texto
                OutlinedTextField(
                    value = uiState.customText,
                    onValueChange = onCustomTextChange,
                    label = { Text("Escribir texto:") },
                    placeholder = { Text("Escribe aquí") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = uiState.selectedText == "Sí"
                )
                
                // Mostrar error
                if (uiState.error != null) {
                    Text(
                        text = uiState.error,
                        color = MaterialTheme.colorScheme.error,
                        fontSize = 14.sp,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Botones
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = {
                            onClearError()
                            onCheckConnection()
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF4CAF50)
                        )
                    ) {
                        Text("Comprobar Conexión", color = Color.White)
                    }
                    
                    Button(
                        onClick = {
                            onClearError()
                            onStartGame()
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2196F3)
                        )
                    ) {
                        Text("Comenzar", color = Color.White)
                    }
                }
            }
        }
    }
}
