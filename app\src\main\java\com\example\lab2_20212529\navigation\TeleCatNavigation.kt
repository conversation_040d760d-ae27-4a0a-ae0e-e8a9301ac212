package com.example.lab2_20212529.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.lab2_20212529.ui.screens.HistoryScreen
import com.example.lab2_20212529.ui.screens.MainScreen
import com.example.lab2_20212529.ui.screens.TeleCatScreen
import com.example.lab2_20212529.viewmodel.TeleCatViewModel

sealed class Screen(val route: String) {
    object Main : Screen("main")
    object TeleCat : Screen("telecat")
    object History : Screen("history")
}

@Composable
fun TeleCatNavigation(
    navController: NavHostController = rememberNavController(),
    viewModel: TeleCatViewModel
) {
    val uiState by viewModel.uiState.collectAsState()
    
    NavHost(
        navController = navController,
        startDestination = Screen.Main.route
    ) {
        composable(Screen.Main.route) {
            MainScreen(
                uiState = uiState,
                onQuantityChange = { quantity ->
                    viewModel.updateQuantity(quantity)
                },
                onTextSelectionChange = { text ->
                    viewModel.updateSelectedText(text)
                },
                onCustomTextChange = { text ->
                    viewModel.updateCustomText(text)
                },
                onCheckConnection = {
                    viewModel.checkConnection()
                },
                onStartGame = {
                    if (viewModel.validateAndProceed()) {
                        viewModel.startTeleCat()
                        navController.navigate(Screen.TeleCat.route)
                    }
                },
                onClearError = {
                    viewModel.clearError()
                }
            )
        }
        
        composable(Screen.TeleCat.route) {
            TeleCatScreen(
                uiState = uiState,
                imageUrl = viewModel.getCurrentImageUrl(),
                onNextImage = {
                    viewModel.nextImage()
                },
                onFinishGame = {
                    viewModel.nextImage() // Esto agregará la interacción al historial
                    navController.navigate(Screen.History.route) {
                        popUpTo(Screen.Main.route)
                    }
                }
            )
        }
        
        composable(Screen.History.route) {
            HistoryScreen(
                uiState = uiState,
                onBackToGame = {
                    viewModel.resetGame()
                    navController.navigate(Screen.Main.route) {
                        popUpTo(Screen.Main.route) {
                            inclusive = true
                        }
                    }
                }
            )
        }
    }
}
