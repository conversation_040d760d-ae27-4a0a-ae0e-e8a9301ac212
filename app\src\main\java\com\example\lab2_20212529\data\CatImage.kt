package com.example.lab2_20212529.data

data class CatImage(
    val id: String,
    val url: String,
    val width: Int,
    val height: Int,
    val tags: List<String> = emptyList()
)

data class AppState(
    val quantity: Int = 3,
    val selectedText: String = "Elegir",
    val customText: String = "",
    val currentImageIndex: Int = 0,
    val images: List<CatImage> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val timeRemaining: Int = 0,
    val interactions: List<Interaction> = emptyList()
)

data class Interaction(
    val id: Int,
    val imageCount: Int,
    val timestamp: Long = System.currentTimeMillis()
)
