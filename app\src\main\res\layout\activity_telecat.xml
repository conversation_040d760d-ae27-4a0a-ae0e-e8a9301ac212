<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#F8BBD9"
    android:gravity="center">

    <!-- Título -->
    <TextView
        android:id="@+id/tvTitulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="¿Listo? Diviértete!"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- Información del tiempo -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Tiempo restante"
            android:textSize="16sp"
            android:textColor="#666666" />

        <TextView
            android:id="@+id/tvTiempo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginStart="8dp"
            android:background="@drawable/time_background"
            android:padding="4dp" />

    </LinearLayout>

    <!-- Información de cantidad -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cantidad = "
            android:textSize="16sp"
            android:textColor="#666666" />

        <TextView
            android:id="@+id/tvCantidad"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#333333" />

    </LinearLayout>

    <!-- Título TeleCat -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="TeleCat!"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="16dp" />

    <!-- Imagen del gato -->
    <ImageView
        android:id="@+id/ivGato"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_marginBottom="16dp"
        android:scaleType="centerCrop"
        android:background="@drawable/image_background"
        android:contentDescription="Imagen de gato" />

    <!-- Botón Siguiente -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnSiguiente"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Siguiente"
        android:backgroundTint="#E57373"
        android:textColor="@android:color/white"
        android:layout_marginTop="16dp" />

</LinearLayout>
