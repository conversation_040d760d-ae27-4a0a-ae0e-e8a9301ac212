package com.example.lab2_20212529.repository

import android.content.Context
import com.example.lab2_20212529.data.CatImage
import com.example.lab2_20212529.network.NetworkModule
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class CatRepository(private val context: Context) {
    
    private val apiService = NetworkModule.catApiService
    
    suspend fun getCatImages(limit: Int): Result<List<CatImage>> {
        return withContext(Dispatchers.IO) {
            try {
                if (!NetworkModule.isNetworkAvailable(context)) {
                    return@withContext Result.failure(Exception("No hay conexión a Internet"))
                }
                
                val response = apiService.getCats(limit = limit)
                if (response.isSuccessful) {
                    val cats = response.body() ?: emptyList()
                    // Crear URLs de imágenes simuladas ya que la API real puede tener estructura diferente
                    val catImages = (1..limit).map { index ->
                        CatImage(
                            id = "cat_$index",
                            url = "https://cataas.com/cat?${System.currentTimeMillis() + index}",
                            width = 400,
                            height = 300
                        )
                    }
                    Result.success(catImages)
                } else {
                    Result.failure(Exception("Error al obtener imágenes: ${response.code()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    fun getCatImageUrl(catId: String, text: String? = null): String {
        return if (text.isNullOrEmpty() || text == "Elegir") {
            "https://cataas.com/cat?${System.currentTimeMillis()}"
        } else {
            "https://cataas.com/cat/says/$text?${System.currentTimeMillis()}"
        }
    }
}
