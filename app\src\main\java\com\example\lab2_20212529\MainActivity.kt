package com.example.lab2_20212529

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.rememberNavController
import com.example.lab2_20212529.navigation.TeleCatNavigation
import com.example.lab2_20212529.ui.theme.Lab2_20212529Theme
import com.example.lab2_20212529.viewmodel.TeleCatViewModel
import com.example.lab2_20212529.viewmodel.TeleCatViewModelFactory

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Lab2_20212529Theme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    val viewModel: TeleCatViewModel = viewModel(
                        factory = TeleCatViewModelFactory(this@MainActivity)
                    )

                    TeleCatNavigation(
                        navController = navController,
                        viewModel = viewModel
                    )
                }
            }
        }
    }
}