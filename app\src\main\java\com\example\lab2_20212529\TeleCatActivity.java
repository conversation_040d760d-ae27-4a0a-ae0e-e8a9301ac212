package com.example.lab2_20212529;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * TeleCatActivity - Pantalla que muestra imágenes de gatos con contador descendente
 * Utiliza hilos para la gestión del tiempo y carga de imágenes
 */
public class TeleCatActivity extends AppCompatActivity {

    private TextView tvTiempo;
    private TextView tvCantidad;
    private ImageView ivGato;
    private MaterialButton btnSiguiente;

    private int cantidadTotal;
    private int cantidadActual;
    private String textoSeleccionado;
    private String textoEscrito;

    // Variables para el contador de tiempo
    private int tiempoRestante; // en segundos
    private Handler timerHandler;
    private Runnable timerRunnable;

    // Executor para cargar imágenes
    private ExecutorService executorService;
    private Handler mainHandler;

    // Lista para guardar el historial
    private List<String> historialImagenes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_telecat);

        // Inicializar vistas
        initViews();

        // Obtener datos del Intent
        getIntentData();

        // Configurar handlers y executors
        setupThreading();

        // Inicializar historial
        historialImagenes = new ArrayList<>();

        // Configurar la actividad
        setupActivity();

        // Cargar primera imagen
        loadNextImage();

        // Iniciar contador
        startTimer();
    }

    private void initViews() {
        tvTiempo = findViewById(R.id.tvTiempo);
        tvCantidad = findViewById(R.id.tvCantidad);
        ivGato = findViewById(R.id.ivGato);
        btnSiguiente = findViewById(R.id.btnSiguiente);
    }

    private void getIntentData() {
        Intent intent = getIntent();
        cantidadTotal = intent.getIntExtra("cantidad", 3);
        textoSeleccionado = intent.getStringExtra("texto");
        textoEscrito = intent.getStringExtra("textoEscrito");
        
        cantidadActual = 0;
    }

    private void setupThreading() {
        timerHandler = new Handler(Looper.getMainLooper());
        executorService = Executors.newFixedThreadPool(2);
        mainHandler = new Handler(Looper.getMainLooper());
    }

    private void setupActivity() {
        // Mostrar cantidad total
        tvCantidad.setText(String.valueOf(cantidadTotal));

        // Calcular tiempo total (4 segundos por imagen según las especificaciones)
        tiempoRestante = cantidadTotal * 4;

        // Configurar botón siguiente
        btnSiguiente.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadNextImage();
            }
        });
    }

    /**
     * Inicia el contador de tiempo usando un hilo
     */
    private void startTimer() {
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                if (tiempoRestante > 0) {
                    // Actualizar UI en el hilo principal
                    updateTimerUI();
                    tiempoRestante--;
                    
                    // Programar siguiente actualización
                    timerHandler.postDelayed(this, 1000);
                } else {
                    // Tiempo agotado, habilitar botón siguiente para ir a finalización
                    btnSiguiente.setText("Siguiente (Vista de Finalización)");
                    btnSiguiente.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            showFinalizationDialog();
                        }
                    });
                }
            }
        };
        
        // Iniciar el timer
        timerHandler.post(timerRunnable);
    }

    private void updateTimerUI() {
        int minutos = tiempoRestante / 60;
        int segundos = tiempoRestante % 60;
        String tiempoFormateado = String.format("%02d:%02d", minutos, segundos);
        tvTiempo.setText(tiempoFormateado);
    }

    /**
     * Carga la siguiente imagen usando un hilo en segundo plano
     */
    private void loadNextImage() {
        if (cantidadActual >= cantidadTotal) {
            showFinalizationDialog();
            return;
        }

        cantidadActual++;
        
        // Usar hilo para cargar imagen
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                // Construir URL de la API
                String imageUrl = buildImageUrl();
                
                // Guardar en historial
                historialImagenes.add(imageUrl);
                
                // Cargar imagen en el hilo principal
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        Glide.with(TeleCatActivity.this)
                            .load(imageUrl)
                            .placeholder(R.drawable.image_background)
                            .error(R.drawable.image_background)
                            .into(ivGato);
                        
                        // Actualizar contador de cantidad
                        tvCantidad.setText(String.valueOf(cantidadTotal - cantidadActual));
                        
                        // Si es la última imagen, cambiar texto del botón
                        if (cantidadActual >= cantidadTotal) {
                            btnSiguiente.setText("Siguiente (Vista de Finalización)");
                        }
                    }
                });
            }
        });
    }

    /**
     * Construye la URL para obtener imagen de gato con texto
     */
    private String buildImageUrl() {
        // URL base de la API cataas.com
        String baseUrl = "https://cataas.com/cat";
        
        // Agregar texto si está disponible
        if (textoEscrito != null && !textoEscrito.isEmpty()) {
            baseUrl += "/says/" + textoEscrito.replace(" ", "%20");
        }
        
        // Agregar parámetros adicionales
        baseUrl += "?width=300&height=300";
        
        // Agregar filtro si se seleccionó
        if (textoSeleccionado != null && !textoSeleccionado.equals("Elegir")) {
            baseUrl += "&filter=" + textoSeleccionado;
        }
        
        return baseUrl;
    }

    /**
     * Muestra el diálogo de finalización
     */
    private void showFinalizationDialog() {
        // Detener timer
        if (timerHandler != null && timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("¿Esta seguro que desea volver a Jugar?");
        builder.setMessage("Y elegir entre \"Sí\" y \"No\"");
        
        builder.setPositiveButton("Sí", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // Reiniciar el juego
                restartGame();
            }
        });
        
        builder.setNegativeButton("No", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // Ir al historial
                showHistorial();
            }
        });
        
        builder.setCancelable(false);
        builder.show();
    }

    private void restartGame() {
        // Reiniciar variables
        cantidadActual = 0;
        tiempoRestante = cantidadTotal * 4;
        
        // Reiniciar UI
        btnSiguiente.setText("Siguiente");
        btnSiguiente.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadNextImage();
            }
        });
        
        // Cargar primera imagen
        loadNextImage();
        
        // Reiniciar timer
        startTimer();
    }

    private void showHistorial() {
        Intent intent = new Intent(this, HistorialActivity.class);
        intent.putStringArrayListExtra("historial", new ArrayList<>(historialImagenes));
        intent.putExtra("cantidadImagenes", historialImagenes.size());
        startActivity(intent);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Limpiar recursos
        if (timerHandler != null && timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
