<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="#F8BBD9"
    android:gravity="center">

    <!-- <PERSON><PERSON><PERSON><PERSON> principal -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="¡Bienvenido a TeleCat 🐱!"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="8dp"
        android:gravity="center" />

    <!-- Subtítulo -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="La app diseñada para generar memes de gatos y divertirte :)"
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginBottom="32dp"
        android:gravity="center" />

    <!-- Campo <PERSON>ti<PERSON> -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Cantidad:"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etCantidad"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Campo Texto (Spinner) -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Texto:"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

        <AutoCompleteTextView
            android:id="@+id/spinnerTexto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:text="Elegir" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Campo Escribir texto -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:hint="Escribir texto:"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etEscribirTexto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Botón Comprobar Conexión -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnComprobarConexion"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🌐 Comprobar Conexión"
        android:layout_marginBottom="16dp"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:textColor="#E57373"
        app:strokeColor="#E57373"
        xmlns:app="http://schemas.android.com/apk/res-auto" />

    <!-- Botón Comenzar -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnComenzar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="▶ Comenzar"
        android:backgroundTint="#E57373"
        android:textColor="@android:color/white" />

</LinearLayout>
