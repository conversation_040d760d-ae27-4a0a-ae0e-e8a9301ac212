<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#F8BBD9">

    <!-- T<PERSON><PERSON>lo -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Historial"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:background="#E57373"
        android:textColor="@android:color/white"
        android:padding="12dp"
        android:layout_marginHorizontal="32dp" />

    <!-- Lista de interacciones -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:id="@+id/llHistorialContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp">

            <!-- Las interacciones se agregarán dinámicamente aquí -->

        </LinearLayout>

    </ScrollView>

    <!-- Botón Volver a Jugar -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnVolverAJugar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🎮 Volver a Jugar"
        android:backgroundTint="#E57373"
        android:textColor="@android:color/white"
        android:layout_marginHorizontal="32dp" />

</LinearLayout>
