package com.example.lab2_20212529.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.lab2_20212529.data.AppState
import com.example.lab2_20212529.data.CatImage
import com.example.lab2_20212529.data.Interaction
import com.example.lab2_20212529.network.NetworkModule
import com.example.lab2_20212529.repository.CatRepository
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class TeleCatViewModel(private val context: Context) : ViewModel() {

    private val repository = CatRepository(context)
    
    private val _uiState = MutableStateFlow(AppState())
    val uiState: StateFlow<AppState> = _uiState.asStateFlow()
    
    private var countdownJob: Job? = null
    
    fun updateQuantity(quantity: String) {
        val numQuantity = quantity.toIntOrNull() ?: 3
        _uiState.value = _uiState.value.copy(quantity = numQuantity)
    }
    
    fun updateSelectedText(text: String) {
        _uiState.value = _uiState.value.copy(selectedText = text)
    }
    
    fun updateCustomText(text: String) {
        _uiState.value = _uiState.value.copy(customText = text)
    }
    
    fun validateAndProceed(): Boolean {
        val state = _uiState.value

        // Validación: Si se eligió "Sí" en Texto, debe escribir algo en "Escribir texto"
        if (state.selectedText == "Sí" && state.customText.isBlank()) {
            _uiState.value = state.copy(error = "Debe escribir algo en 'Escribir texto'")
            return false
        }

        // Validación: La cantidad debe ser mayor a 0
        if (state.quantity <= 0) {
            _uiState.value = state.copy(error = "La cantidad debe ser mayor a 0")
            return false
        }

        _uiState.value = state.copy(error = null)
        return true
    }

    fun checkConnection(): Boolean {
        return if (NetworkModule.isNetworkAvailable(context)) {
            _uiState.value = _uiState.value.copy(error = "✅ Conexión exitosa a Internet")
            true
        } else {
            _uiState.value = _uiState.value.copy(error = "❌ No hay conexión a Internet")
            false
        }
    }
    
    fun startTeleCat() {
        if (!validateAndProceed()) return
        
        _uiState.value = _uiState.value.copy(isLoading = true)
        
        viewModelScope.launch {
            val result = repository.getCatImages(_uiState.value.quantity)
            result.fold(
                onSuccess = { images ->
                    _uiState.value = _uiState.value.copy(
                        images = images,
                        isLoading = false,
                        currentImageIndex = 0,
                        timeRemaining = 12 // 12 segundos por imagen
                    )
                    startCountdown()
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message ?: "Error desconocido"
                    )
                }
            )
        }
    }
    
    private fun startCountdown() {
        countdownJob?.cancel()
        countdownJob = viewModelScope.launch {
            var timeLeft = _uiState.value.timeRemaining
            while (timeLeft > 0) {
                delay(1000)
                timeLeft--
                _uiState.value = _uiState.value.copy(timeRemaining = timeLeft)
            }
            // Cuando el tiempo se acaba, habilitar el botón "siguiente"
        }
    }
    
    fun nextImage() {
        val state = _uiState.value
        val nextIndex = state.currentImageIndex + 1
        
        if (nextIndex < state.images.size) {
            _uiState.value = state.copy(
                currentImageIndex = nextIndex,
                timeRemaining = 12
            )
            startCountdown()
        } else {
            // Terminar el juego y agregar interacción al historial
            val interaction = Interaction(
                id = state.interactions.size + 1,
                imageCount = state.images.size
            )
            _uiState.value = state.copy(
                interactions = state.interactions + interaction
            )
        }
    }
    
    fun getCurrentImageUrl(): String {
        val state = _uiState.value
        if (state.images.isEmpty()) return ""
        
        val currentImage = state.images[state.currentImageIndex]
        val text = if (state.selectedText == "Sí") state.customText else null
        return repository.getCatImageUrl(currentImage.id, text)
    }
    
    fun resetGame() {
        countdownJob?.cancel()
        _uiState.value = AppState(
            interactions = _uiState.value.interactions
        )
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
    }
}
