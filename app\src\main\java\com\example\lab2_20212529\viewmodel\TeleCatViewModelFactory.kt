package com.example.lab2_20212529.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

class TeleCatViewModelFactory(private val context: Context) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(TeleCatViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return TeleCatViewModel(context) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
