package com.example.lab2_20212529;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MainActivity - Pantalla principal de TeleCat
 * Permite al usuario configurar la cantidad de imágenes y texto para los memes
 * Utiliza hilos para verificar la conectividad a Internet
 */
public class MainActivity extends AppCompatActivity {

    private TextInputEditText etCantidad;
    private AutoCompleteTextView spinnerTexto;
    private TextInputEditText etEscribirTexto;
    private MaterialButton btnComprobarConexion;
    private MaterialButton btnComenzar;

    // Executor para manejar hilos en segundo plano
    private ExecutorService executorService;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Inicializar componentes
        initViews();
        setupSpinner();
        setupClickListeners();

        // Inicializar executor y handler para hilos
        executorService = Executors.newFixedThreadPool(2);
        mainHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * Inicializa las vistas del layout
     */
    private void initViews() {
        etCantidad = findViewById(R.id.etCantidad);
        spinnerTexto = findViewById(R.id.spinnerTexto);
        etEscribirTexto = findViewById(R.id.etEscribirTexto);
        btnComprobarConexion = findViewById(R.id.btnComprobarConexion);
        btnComenzar = findViewById(R.id.btnComenzar);
    }

    /**
     * Configura el spinner con opciones predefinidas
     */
    private void setupSpinner() {
        String[] opciones = {"hello", "cute", "funny", "sleepy", "angry"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_dropdown_item_1line, opciones);
        spinnerTexto.setAdapter(adapter);
    }

    /**
     * Configura los listeners de los botones
     */
    private void setupClickListeners() {
        btnComprobarConexion.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Usar hilo para verificar conexión a Internet
                checkInternetConnection();
            }
        });

        btnComenzar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (validateInputs()) {
                    startTeleCatActivity();
                }
            }
        });
    }

    /**
     * Verifica la conexión a Internet usando un hilo en segundo plano
     * Muestra el resultado en un Toast
     */
    private void checkInternetConnection() {
        // Primero verificar conectividad local
        if (!isNetworkAvailable()) {
            Toast.makeText(this, "Error Toast: No hay conexión de red", Toast.LENGTH_SHORT).show();
            return;
        }

        // Usar hilo para verificar conectividad real a Internet
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                boolean hasInternet = false;
                try {
                    // Intentar conexión a Google DNS
                    URL url = new URL("https://www.google.com");
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("HEAD");
                    connection.setConnectTimeout(3000);
                    connection.setReadTimeout(3000);
                    
                    int responseCode = connection.getResponseCode();
                    hasInternet = (responseCode == 200);
                    connection.disconnect();
                } catch (IOException e) {
                    hasInternet = false;
                }

                final boolean finalHasInternet = hasInternet;
                // Volver al hilo principal para mostrar el resultado
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (finalHasInternet) {
                            Toast.makeText(MainActivity.this, "Success Toast: Conexión a Internet exitosa", 
                                Toast.LENGTH_SHORT).show();
                        } else {
                            Toast.makeText(MainActivity.this, "Error Toast: No se puede conectar a Internet", 
                                Toast.LENGTH_SHORT).show();
                        }
                    }
                });
            }
        });
    }

    /**
     * Verifica si hay conectividad de red disponible
     */
    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = 
            (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    /**
     * Valida las entradas del usuario
     */
    private boolean validateInputs() {
        String cantidad = etCantidad.getText().toString().trim();
        String textoSeleccionado = spinnerTexto.getText().toString().trim();
        String textoEscrito = etEscribirTexto.getText().toString().trim();

        // Validar cantidad
        if (cantidad.isEmpty()) {
            etCantidad.setError("Debe escribir algo en 'Cantidad'");
            etCantidad.requestFocus();
            return false;
        }

        int cantidadNum;
        try {
            cantidadNum = Integer.parseInt(cantidad);
            if (cantidadNum <= 0) {
                etCantidad.setError("La cantidad debe ser mayor a 0");
                etCantidad.requestFocus();
                return false;
            }
        } catch (NumberFormatException e) {
            etCantidad.setError("Debe ingresar un número válido");
            etCantidad.requestFocus();
            return false;
        }

        // Validar texto (debe elegir una opción)
        if (textoSeleccionado.equals("Elegir") || textoSeleccionado.isEmpty()) {
            Toast.makeText(this, "Debe elegir una opción en 'Texto'", Toast.LENGTH_SHORT).show();
            return false;
        }

        // Validar texto escrito
        if (textoEscrito.isEmpty()) {
            etEscribirTexto.setError("Debe escribir algo en 'Escribir texto'");
            etEscribirTexto.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * Inicia la actividad TeleCat con los parámetros configurados
     */
    private void startTeleCatActivity() {
        Intent intent = new Intent(this, TeleCatActivity.class);
        intent.putExtra("cantidad", Integer.parseInt(etCantidad.getText().toString().trim()));
        intent.putExtra("texto", spinnerTexto.getText().toString().trim());
        intent.putExtra("textoEscrito", etEscribirTexto.getText().toString().trim());
        startActivity(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Limpiar recursos del executor
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
